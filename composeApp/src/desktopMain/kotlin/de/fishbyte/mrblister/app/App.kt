package de.fishbyte.mrblister.app

import Dialog
import HistoryView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.views.*
import de.fishbyte.mrblister.app.views.calibration.CalibrationView
import de.fishbyte.mrblister.app.views.calibration.CalibrationViewModel
import de.fishbyte.mrblister.app.views.history.HistoryViewModel
import de.fishbyte.mrblister.app.views.job.BlisterJobView
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import de.fishbyte.mrblister.app.views.jobimport.JobImportView
import de.fishbyte.mrblister.app.views.jobimport.JobImportViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListView
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.login.LoginView
import de.fishbyte.mrblister.app.views.login.LoginViewModel
import de.fishbyte.mrblister.app.views.startup.LoadingView
import de.fishbyte.mrblister.app.views.usermanagement.UserManagementView
import de.fishbyte.mrblister.app.views.usermanagement.UserManagementViewModel
import de.fishbyte.mrblister.app.views.blisterconfig.BlisterConfigurationManagementView
import de.fishbyte.mrblister.app.views.blisterconfig.BlisterConfigurationManagementViewModel
import org.koin.compose.getKoin

@Composable
fun App( settingsPath: String? ) {

    val appViewModel = getKoin().get<AppViewModel>()

    val navigation = getKoin().get<AppNavigation>()
    val viewModelStack by navigation.viewModelStack.collectAsState()
    val alertMessage by appViewModel.alertMessage.collectAsState()
    val currentUser by appViewModel.user.collectAsState()
    val dialog by appViewModel.dialog.collectAsState()
    val logger = appViewModel.logger

    AppTheme {
        Box(modifier = Modifier.fillMaxSize().padding(appViewModel.settings.overscan?.dp ?:0.dp)) {
            when (val currentViewModel = viewModelStack.lastOrNull()) {
                is BlisterJobViewModel -> BlisterJobView(currentViewModel)
                is JobListViewModel -> JobListView(currentViewModel, logger)
                is LoginViewModel -> LoginView()
                is JobImportViewModel -> JobImportView(currentViewModel)
                is CalibrationViewModel -> CalibrationView(currentViewModel)
                is HistoryViewModel -> HistoryView(currentViewModel)
                is UserManagementViewModel -> UserManagementView()
                is BlisterConfigurationManagementViewModel -> BlisterConfigurationManagementView()
                null -> LoadingView(settingsPath)
            }

            dialog?.let { dialogContent ->
                dialogContent()
            }

            if (alertMessage != null) {

                Dialog("Fehler", alertMessage!!, onConfirm = { appViewModel.dismissAlert() })
                /*
            AlertDialog(
                onDismissRequest = { appViewModel.dismissAlert() },
                title = { Text("Fehler") },
                text = { Text(alertMessage!!) },
                confirmButton = {
                    Button(onClick = { appViewModel.dismissAlert() }) {
                        Text("OK")
                    }
                }
            )

             */
            }
        }
        /*
        NavHost(navController = navController, startDestination = AppWorkflows.PATIENTS.name) {
            composable(route = AppWorkflows.PATIENTS.name) {
                patientsWithJobsList(
                    patientList = viewModel.blisterOrder?.patients?.patientList ?: emptyList(),
                    startBlister = { patient -> navController.navigate( AppWorkflows.CONFIRM_JOB.name + "/" + patient.id + "/" + patient.jobs.jobList[0].id) })
            }
            composable(route = AppWorkflows.CONFIRM_JOB.name + "/{patient}/{job}",
                arguments = listOf(
                    navArgument("patient") { type = NavType.StringType },
                    navArgument("job") { type = NavType.StringType })
            ) {
                val patientID = it.arguments?.getString("patient") ?: ""
                val jobID = it.arguments?.getString("job") ?: ""
                viewModel.blisterOrder?.let { blisterOrder ->
                    blisterOrder.patientByID(patientID)?.let { patient ->
                        patient?.let { it.jobByID(jobID) }?.let { job->

                            blisterJob(viewModel, patient,job,
                                goBack = { navController.navigateUp() },
                                confirm = { navController.navigate(AppWorkflows.PREPARE_MEDICATIONS.name + "/" + patient?.id + "/" + patient.jobs.jobList[0]?.id)}
                            )
                        }
                    }
                }
            }
            composable(route = AppWorkflows.PREPARE_MEDICATIONS.name + "/{patient}/{job}",
                arguments = listOf(
                    navArgument("patient") { type = NavType.StringType },
                    navArgument("job") { type = NavType.StringType })
            ) {

            }
        }

         */
    }

    /*
    val navGraph by remember(navController) {
            navController.createGraph(startDestination = PatientsWithJobs) {
                composable<PatientsWithJobs> {

                }
            }
        }

/ *
        NavHost(
            navController = navController,
            startDestination = PatientsWithJobs,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            composable<PatientsWithJobs>  {
                patientsWithJobsList(
                    patientList = loadBlister("/Volumes/HOME/Users/<USER>/Workspace/MrBlister/sample/Bernd_Blutzucker_5er_Pflegedienst_Herzlich_03510_15.10.24_13.22.xml").patients.patientList,
                    startBlister = { patient -> navController.navigate( route = AppWorkflows.BLISTER.name ) } )

            }
            composable(route = AppWorkflows.START.name) {

                //SettingsWorkflow()

                /*
                StartOrderScreen(
                    quantityOptions = DataSource.quantityOptions,
                    onNextButtonClicked = {
                        viewModel.setQuantity(it)
                        navController.navigate(CupcakeScreen.Flavor.name)
                    },
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                )

                 */
            }
            composable(route = AppWorkflows.SETTINGS.name) {
            }
        }

 */
}

