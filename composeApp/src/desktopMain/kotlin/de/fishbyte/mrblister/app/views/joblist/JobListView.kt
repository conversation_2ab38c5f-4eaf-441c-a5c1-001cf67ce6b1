package de.fishbyte.mrblister.app.views.joblist

import Dialog
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import compose.icons.FontAwesomeIcons
import compose.icons.fontawesomeicons.Regular
import compose.icons.fontawesomeicons.regular.*
import de.fishbyte.mrblister.app.theme.AppTheme
import de.fishbyte.mrblister.app.views.calibration.CalibrationView
import de.fishbyte.mrblister.app.views.calibration.CalibrationViewModel
import de.fishbyte.mrblister.app.views.history.HistoryViewModel
import de.fishbyte.mrblister.app.views.job.BlisterJobViewModel
import de.fishbyte.mrblister.app.views.jobimport.JobImportViewModel
import de.fishbyte.mrblister.app.views.tools.formatShortDate
import de.fishbyte.mrblister.app.views.tools.generateBarcodeHtml
import de.fishbyte.mrblister.app.views.tools.join
import de.fishbyte.mrblister.app.views.tools.name
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.RootViewComponent
import de.fishbyte.mrblister.model.JobStatus
import de.fishbyte.mrblister.model.NursingHome
import de.fishbyte.mrblister.model.getNameAndStation
import de.fishbyte.mrblister.model.getStation
import de.fishbyte.mrblister.services.LoggerService
import de.fishbyte.mrblister.services.SearchService
import kotlinx.coroutines.flow.map
import org.koin.compose.getKoin
import java.time.LocalDate
import java.time.temporal.WeekFields
import java.util.*
import kotlin.system.exitProcess

@Composable
fun JobListView(viewModel: JobListViewModel, logger: LoggerService) {

    val theme = getKoin().get<AppTheme>()

    val coroutineScope = rememberCoroutineScope()

    val jobs = viewModel.appViewModel.jobs.collectAsState()
    val blisterWidth = viewModel.appViewModel.settings.blisterWidth.dp
    val searchService = getKoin().get<SearchService>()
    val logger = viewModel.appViewModel.logger
    val group = viewModel.group.collectAsState()


    RootViewComponent(footer = {
        Components.secondaryButton("Verlauf", { HistoryViewModel(viewModel.appViewModel,searchService).present(viewModel.appViewModel) })
        // Only show user management button for admin users
        if (viewModel.appViewModel.user.value?.role == de.fishbyte.mrblister.model.UserRole.ADMIN) {
            Components.secondaryButton("Kalibrierung", { CalibrationViewModel(viewModel.appViewModel).present(viewModel.appViewModel) })
            Components.secondaryButton("Konfigurationen", { de.fishbyte.mrblister.app.views.blisterconfig.BlisterConfigurationManagementViewModel.present(viewModel.appViewModel) })
            Components.secondaryButton("Benutzerverwaltung", { de.fishbyte.mrblister.app.views.usermanagement.UserManagementViewModel.present(viewModel.appViewModel) })
        }
        //Components.secondaryButton("Barcodes", { generateBarcodeHtml(viewModel.appViewModel.blisterJobs.value.values.toList(),"${System.getProperty("user.home")}/barcodes.html", logger) })
        Components.primaryButton("Abmelden",  { viewModel.appViewModel.logout() } )
        Components.secondaryButton("Beenden", { exitProcess(0) } )
    }) {

        Box(modifier = Modifier.fillMaxSize()) {

            Row {
                Column(modifier = Modifier.weight(1f)) {
                    if(group.value?.isNotEmpty() == true) {
                        patientList(jobs, viewModel, theme, logger)
                    } else {
                        groupList(jobs, viewModel, theme, logger)
                    }
                }
                Spacer(modifier = Modifier.width(48.dp))
                Box(modifier = Modifier.fillMaxHeight().width(blisterWidth), contentAlignment = Alignment.TopCenter) {
                    Text(
                        "Bitte einen Patienten scannen.",
                        modifier = Modifier.padding(8.dp),
                        style = MaterialTheme.typography.headlineLarge,
                        color = MaterialTheme.colorScheme.tertiary
                    )
                }
            }
        }
    }
}


@Composable
fun groupList(jobs: State<List<BlisterJobViewModel>>, viewModel: JobListViewModel, theme: AppTheme, logger: LoggerService)
{
    val searchService = getKoin().get<SearchService>()
    Column {
        Button(
            onClick = { JobImportViewModel.present(viewModel.appViewModel, searchService) },
            modifier = Modifier.padding(bottom = 16.dp)
        ) {
            Text("+ Neue Aufträge hinzufügen")
        }
        if (jobs.value.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    "Keine Aufträge offen.",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.tertiary
                )
            }
        } else {
            // Get all nursing homes from jobs and filter distinct ones by name and station
            val nursingHomes = jobs.value
                .mapNotNull { it.job.patient.nursingHome }
                .distinctBy { nh -> "${nh.getNameAndStation() ?: ""}|${nh.getNameAndStation() ?: ""}" }
                .sortedBy { it.name }

            LazyColumn {
                items(nursingHomes) { nursingHome ->
                    nursingHomeItem(nursingHome, theme) {
                        viewModel.selectGroup(nursingHome)
                    }
                    Spacer(
                        modifier = theme.vertical_spacing_m
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun nursingHomeItem(nursingHome: NursingHome, theme: AppTheme, onClick: () -> Unit) {
    Surface(
        shape = MaterialTheme.shapes.medium,
        modifier = Modifier.fillMaxWidth()
    ) {
        ListItem(
            leadingContent = {
                Icon(
                    imageVector = FontAwesomeIcons.Regular.Hospital,
                    contentDescription = "Nursing Home",
                    modifier = theme.icon_size_m
                )
            },
            headlineContent = {
                Text(nursingHome.name ?: "Unbekannt")
            },
            supportingContent = {
                if (nursingHome.station != null) {
                    Text("Station: ${nursingHome.getStation()}")
                }
            },
            trailingContent = {
                Button(
                    onClick = { onClick() },
                    content = {
                        Text("Auswählen")
                    }
                )
            }
        )
    }
}

@Composable
fun patientList(jobs: State<List<BlisterJobViewModel>>, viewModel: JobListViewModel, theme: AppTheme, logger: LoggerService)
{
    val filtered = jobs.value.filter { it.job.patient.nursingHome.getNameAndStation() == viewModel.group.value }
    val searchService = getKoin().get<SearchService>()
    Column {
        Button(
            onClick = { viewModel.selectGroup(null) },
            modifier = Modifier.padding(bottom = 16.dp)
        ) {
            Text("< Zurück")
        }
        if (filtered.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    "Keine Aufträge offen.",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.tertiary
                )
            }
        } else {
            LazyColumn {
                items(filtered, key = { "${it.job.localID}" }) { job ->
                    patientItem(job, theme) {
                        startManualJob(job,viewModel, logger)
                    }
                    Spacer(
                        modifier = theme.vertical_spacing_m
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun patientItem(job: BlisterJobViewModel, theme: AppTheme, startBlister: (BlisterJobViewModel) -> Unit) {


    Surface(
        shape = MaterialTheme.shapes.medium,
        modifier = Modifier.fillMaxWidth()
    ) {
        ListItem(
            leadingContent = {
                Icon(
                    imageVector = when (job.jobStatus) {
                        JobStatus.Pending -> {
                            FontAwesomeIcons.Regular.Circle
                        }

                        JobStatus.Completed -> {
                            FontAwesomeIcons.Regular.CheckCircle
                        }

                        JobStatus.InProgress -> {
                            FontAwesomeIcons.Regular.PlayCircle
                        }

                        JobStatus.Paused -> {
                            FontAwesomeIcons.Regular.PauseCircle
                        }

                        JobStatus.Cancelled -> {
                            FontAwesomeIcons.Regular.TrashAlt
                        }
                    },
                    contentDescription = job.jobStatus.name,
                    modifier = theme.icon_size_m
                )
            },


            headlineContent = {
                Text( join(name(job.job.patient), job.job.getIndexLocalized()))
                              },
            supportingContent = { Text(join( formatShortDate(job.job.patient.birthday),job.job.patient.nursingHome?.name)) },
            /*overlineContent = { if (job.patient.jobs.jobList.isNotEmpty()) Text("${job.job.id}") else null },*/
            trailingContent = {
                    Button(
                        onClick = { startBlister(job) },
                        content = {
                            Text("Manuell")
                        }
                    )
            }
        )
    }
}



fun startManualJob(job: BlisterJobViewModel, viewModel: JobListViewModel, logger: LoggerService) {

    if( viewModel.appViewModel.settings.patientScanNag ) {
        viewModel.appViewModel.showDialog {
            Dialog("Manuell starten",
                "Bitte Barcode des Patienten scannen. Manuelle Auswahl nur in Ausnahmefällen möglich.",
                onCloseMessage = "zurück zum Scannen",
                onClose = {
                    viewModel.appViewModel.dismissDialog()
                },
                onConfirmMessage = "Blistervorgang ohne scannen starten",
                onConfirm = {
                    startManuelJobImpl(job,viewModel,logger)
                })
        }
    } else {
        startManuelJobImpl(job,viewModel,logger)
    }
}

private fun startManuelJobImpl(job: BlisterJobViewModel, viewModel: JobListViewModel, logger: LoggerService) {
    val medications = job.medications.value.flatMap { it.medications }
    val firstDate = medications.minByOrNull { it.takingDate }?.takingDate
    val calendarWeek = firstDate?.get(WeekFields.ISO.weekOfWeekBasedYear())

    logger.info("Auftrag ${job.job.id} für Patient ${job.job.patient.id} / ${job.job.patient.nursingHome?.name ?: "-"} / KW ${calendarWeek} wurde manuell gestartet")
    viewModel.startJob(job)
}

