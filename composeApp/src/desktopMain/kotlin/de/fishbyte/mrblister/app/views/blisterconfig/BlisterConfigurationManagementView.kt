package de.fishbyte.mrblister.app.views.blisterconfig

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.components.Components
import de.fishbyte.mrblister.components.RootViewComponent
import de.fishbyte.mrblister.model.BlisterConfiguration
import org.koin.mp.KoinPlatform.getKoin

@Composable
fun ErrorMessage(message: String, onDismiss: () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = message,
                color = MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.weight(1f)
            )
            IconButton(onClick = onDismiss) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "Schließen",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

@Composable
fun SuccessMessage(message: String, onDismiss: () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFF4CAF50))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = message,
                color = Color.White,
                modifier = Modifier.weight(1f)
            )
            IconButton(onClick = onDismiss) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "Schließen",
                    tint = Color.White
                )
            }
        }
    }
}

@Composable
fun BlisterConfigurationManagementView() {
    val appViewModel = getKoin().get<AppViewModel>()
    val viewModel = remember { BlisterConfigurationManagementViewModel(appViewModel) }

    val configurations by viewModel.configurations.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()
    val selectedConfiguration by viewModel.selectedConfiguration.collectAsState()
    val isEditing by viewModel.isEditing.collectAsState()

    // State for dialogs
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var configToDelete by remember { mutableStateOf<BlisterConfiguration?>(null) }

    RootViewComponent(
        footer = {
            Components.secondaryButton(
                text = "Zurück",
                onClick = { JobListViewModel.present(viewModel.appViewModel) }
            )
            Spacer(modifier = Modifier.weight(1f))
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = { JobListViewModel.present(viewModel.appViewModel) }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Zurück")
                }
                Text(
                    "Blister-Konfigurationsverwaltung",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Error message
            errorMessage?.let { message ->
                ErrorMessage(message) {
                    viewModel.dismissErrorMessage()
                }
            }

            // Success message
            successMessage?.let { message ->
                SuccessMessage(message) {
                    viewModel.dismissSuccessMessage()
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Main content
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Configuration list
                ConfigurationList(
                    configurations = configurations,
                    onEditConfiguration = { config ->
                        viewModel.selectConfiguration(config)
                        viewModel.startEditing()
                    },
                    onDeleteConfiguration = { config ->
                        configToDelete = config
                        showDeleteConfirmDialog = true
                    },
                    onCalibrateConfiguration = { config ->
                        viewModel.openCalibration(config)
                    },
                    modifier = Modifier.weight(1f)
                )

                // Add/Edit configuration form
                if (isEditing) {
                    EditConfigurationForm(
                        viewModel = viewModel,
                        modifier = Modifier.weight(1f)
                    )
                } else {
                    AddConfigurationForm(
                        viewModel = viewModel,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }

        // Delete confirmation dialog
        if (showDeleteConfirmDialog && configToDelete != null) {
            DeleteConfigurationConfirmDialog(
                configuration = configToDelete!!,
                onDismiss = { confirmed ->
                    if (confirmed) {
                        viewModel.removeConfiguration(configToDelete!!)
                    }
                    showDeleteConfirmDialog = false
                    configToDelete = null
                }
            )
        }
    }
}

@Composable
fun ConfigurationList(
    configurations: List<BlisterConfiguration>,
    onEditConfiguration: (BlisterConfiguration) -> Unit,
    onDeleteConfiguration: (BlisterConfiguration) -> Unit,
    onCalibrateConfiguration: (BlisterConfiguration) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Blister-Konfigurationen",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Table header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.surfaceVariant)
                    .padding(8.dp)
            ) {
                Text(
                    "Name",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(2f)
                )
                Text(
                    "Zeilen",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "Zeit umgedreht",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "Stationen",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(2f)
                )
                Text(
                    "Aktionen",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.width(160.dp)
                )
            }

            // Configuration list
            LazyColumn(
                modifier = Modifier.fillMaxWidth()
            ) {
                items(configurations) { config ->
                    ConfigurationRow(
                        configuration = config,
                        onEditConfiguration = { onEditConfiguration(config) },
                        onDeleteConfiguration = { onDeleteConfiguration(config) },
                        onCalibrateConfiguration = { onCalibrateConfiguration(config) }
                    )
                }
            }
        }
    }
}

@Composable
fun ConfigurationRow(
    configuration: BlisterConfiguration,
    onEditConfiguration: () -> Unit,
    onDeleteConfiguration: () -> Unit,
    onCalibrateConfiguration: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            configuration.name,
            modifier = Modifier.weight(2f)
        )
        Text(
            configuration.rowHeight.size.toString(),
            modifier = Modifier.weight(1f)
        )
        Text(
            if (configuration.flipTime == true) "Ja" else "Nein",
            modifier = Modifier.weight(1f)
        )
        Text(
            configuration.stations.joinToString(", "),
            modifier = Modifier.weight(2f)
        )
        Row(
            modifier = Modifier.width(160.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            IconButton(
                onClick = onEditConfiguration,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "Bearbeiten",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            IconButton(
                onClick = onDeleteConfiguration,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = "Löschen",
                    tint = MaterialTheme.colorScheme.error
                )
            }
            IconButton(
                onClick = onCalibrateConfiguration,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    Icons.Default.Settings,
                    contentDescription = "Kalibrieren",
                    tint = MaterialTheme.colorScheme.secondary
                )
            }
        }
    }
}

@Composable
fun AddConfigurationForm(
    viewModel: BlisterConfigurationManagementViewModel,
    modifier: Modifier = Modifier
) {
    val newConfigName by viewModel.newConfigName.collectAsState()
    val newConfigRows by viewModel.newConfigRows.collectAsState()
    val newConfigFlipTime by viewModel.newConfigFlipTime.collectAsState()
    val newConfigStations by viewModel.newConfigStations.collectAsState()

    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Neue Konfiguration hinzufügen",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Form fields
            OutlinedTextField(
                value = newConfigName,
                onValueChange = viewModel::updateNewConfigName,
                label = { Text("Konfigurationsname") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = newConfigRows.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { viewModel.updateNewConfigRows(it) }
                },
                label = { Text("Anzahl Zeilen") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = newConfigFlipTime,
                    onCheckedChange = viewModel::updateNewConfigFlipTime
                )
                Text(
                    "Zeit umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = newConfigStations,
                onValueChange = viewModel::updateNewConfigStations,
                label = { Text("Stationen (kommagetrennt)") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("z.B. Station 1, Station 2, Station 3") }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Submit button
            Button(
                onClick = viewModel::addConfiguration,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("Konfiguration hinzufügen")
            }
        }
    }
}

@Composable
fun EditConfigurationForm(
    viewModel: BlisterConfigurationManagementViewModel,
    modifier: Modifier = Modifier
) {
    val editConfigName by viewModel.editConfigName.collectAsState()
    val editConfigRows by viewModel.editConfigRows.collectAsState()
    val editConfigFlipTime by viewModel.editConfigFlipTime.collectAsState()
    val editConfigStations by viewModel.editConfigStations.collectAsState()

    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                "Konfiguration bearbeiten",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Form fields
            OutlinedTextField(
                value = editConfigName,
                onValueChange = viewModel::updateEditConfigName,
                label = { Text("Konfigurationsname") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigRows.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { viewModel.updateEditConfigRows(it) }
                },
                label = { Text("Anzahl Zeilen") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = editConfigFlipTime,
                    onCheckedChange = viewModel::updateEditConfigFlipTime
                )
                Text(
                    "Zeit umgedreht",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = editConfigStations,
                onValueChange = viewModel::updateEditConfigStations,
                label = { Text("Stationen (kommagetrennt)") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("z.B. Station 1, Station 2, Station 3") }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = viewModel::cancelEditing,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Abbrechen")
                }
                Button(
                    onClick = viewModel::updateConfiguration,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Speichern")
                }
            }
        }
    }
}

@Composable
fun DeleteConfigurationConfirmDialog(
    configuration: BlisterConfiguration,
    onDismiss: (Boolean) -> Unit
) {
    AlertDialog(
        onDismissRequest = { onDismiss(false) },
        title = { Text("Konfiguration löschen") },
        text = {
            Text("Möchten Sie die Konfiguration '${configuration.name}' wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.")
        },
        confirmButton = {
            Button(
                onClick = { onDismiss(true) },
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) {
                Text("Löschen")
            }
        },
        dismissButton = {
            OutlinedButton(onClick = { onDismiss(false) }) {
                Text("Abbrechen")
            }
        }
    )
}