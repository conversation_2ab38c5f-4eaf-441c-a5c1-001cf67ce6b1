package de.fishbyte.mrblister.app.views.blisterconfig

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import de.fishbyte.mrblister.app.AppViewModel
import de.fishbyte.mrblister.app.views.joblist.JobListViewModel
import de.fishbyte.mrblister.app.views.calibration.CalibrationViewModel
import de.fishbyte.mrblister.model.BlisterConfiguration
import de.fishbyte.mrblister.tools.KeyboardHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class BlisterConfigurationManagementViewModel(val appViewModel: AppViewModel) : ViewModel(), KeyboardHandler {
    private val _configurations = MutableStateFlow<List<BlisterConfiguration>>(emptyList())
    val configurations: StateFlow<List<BlisterConfiguration>> get() = _configurations.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> get() = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> get() = _successMessage.asStateFlow()

    private val _selectedConfiguration = MutableStateFlow<BlisterConfiguration?>(null)
    val selectedConfiguration: StateFlow<BlisterConfiguration?> get() = _selectedConfiguration.asStateFlow()

    // New configuration form fields
    private val _newConfigName = MutableStateFlow("")
    val newConfigName: StateFlow<String> get() = _newConfigName.asStateFlow()

    private val _newConfigRows = MutableStateFlow(4)
    val newConfigRows: StateFlow<Int> get() = _newConfigRows.asStateFlow()

    private val _newConfigFlipTime = MutableStateFlow(false)
    val newConfigFlipTime: StateFlow<Boolean> get() = _newConfigFlipTime.asStateFlow()

    private val _newConfigStations = MutableStateFlow("")
    val newConfigStations: StateFlow<String> get() = _newConfigStations.asStateFlow()

    // Edit configuration form fields
    private val _editConfigName = MutableStateFlow("")
    val editConfigName: StateFlow<String> get() = _editConfigName.asStateFlow()

    private val _editConfigRows = MutableStateFlow(4)
    val editConfigRows: StateFlow<Int> get() = _editConfigRows.asStateFlow()

    private val _editConfigFlipTime = MutableStateFlow(false)
    val editConfigFlipTime: StateFlow<Boolean> get() = _editConfigFlipTime.asStateFlow()

    private val _editConfigStations = MutableStateFlow("")
    val editConfigStations: StateFlow<String> get() = _editConfigStations.asStateFlow()

    private val _isEditing = MutableStateFlow(false)
    val isEditing: StateFlow<Boolean> get() = _isEditing.asStateFlow()

    init {
        refreshConfigurations()
    }

    private fun refreshConfigurations() {
        _configurations.value = appViewModel.blisterConfiguration
    }

    fun updateNewConfigName(name: String) {
        _newConfigName.value = name
        _errorMessage.value = null
    }

    fun updateNewConfigRows(rows: Int) {
        _newConfigRows.value = rows.coerceIn(1, 10)
        _errorMessage.value = null
    }

    fun updateNewConfigFlipTime(flipTime: Boolean) {
        _newConfigFlipTime.value = flipTime
        _errorMessage.value = null
    }

    fun updateNewConfigStations(stations: String) {
        _newConfigStations.value = stations
        _errorMessage.value = null
    }

    fun updateEditConfigName(name: String) {
        _editConfigName.value = name
        _errorMessage.value = null
    }

    fun updateEditConfigRows(rows: Int) {
        _editConfigRows.value = rows.coerceIn(1, 10)
        _errorMessage.value = null
    }

    fun updateEditConfigFlipTime(flipTime: Boolean) {
        _editConfigFlipTime.value = flipTime
        _errorMessage.value = null
    }

    fun updateEditConfigStations(stations: String) {
        _editConfigStations.value = stations
        _errorMessage.value = null
    }

    fun selectConfiguration(config: BlisterConfiguration) {
        _selectedConfiguration.value = config
        _editConfigName.value = config.name
        _editConfigRows.value = config.rowHeight.size
        _editConfigFlipTime.value = config.flipTime ?: false
        _editConfigStations.value = config.stations.joinToString(", ")
        _errorMessage.value = null
    }

    fun startEditing() {
        _isEditing.value = true
    }

    fun cancelEditing() {
        _isEditing.value = false
        _selectedConfiguration.value = null
        clearEditForm()
    }

    fun clearNewConfigForm() {
        _newConfigName.value = ""
        _newConfigRows.value = 4
        _newConfigFlipTime.value = false
        _newConfigStations.value = ""
    }

    fun clearEditForm() {
        _editConfigName.value = ""
        _editConfigRows.value = 4
        _editConfigFlipTime.value = false
        _editConfigStations.value = ""
    }

    fun dismissErrorMessage() {
        _errorMessage.value = null
    }

    fun dismissSuccessMessage() {
        _successMessage.value = null
    }

    fun addConfiguration() {
        viewModelScope.launch {
            try {
                // Validate input
                if (newConfigName.value.isBlank()) {
                    _errorMessage.value = "Konfigurationsname darf nicht leer sein"
                    return@launch
                }

                // Check if configuration already exists
                if (configurations.value.any { it.name == newConfigName.value }) {
                    _errorMessage.value = "Eine Konfiguration mit diesem Namen existiert bereits"
                    return@launch
                }

                // Parse stations
                val stationsList = if (newConfigStations.value.isBlank()) {
                    emptyList()
                } else {
                    newConfigStations.value.split(",").map { it.trim() }.filter { it.isNotBlank() }
                }

                // Create default column widths (7 columns)
                val defaultColumnWidths = List(7) { 180f }

                // Create row heights based on number of rows
                val defaultRowHeights = List(newConfigRows.value) { 190f }

                // Create new configuration
                val newConfig = BlisterConfiguration(
                    name = newConfigName.value,
                    columnWidth = defaultColumnWidths,
                    rowHeight = defaultRowHeights,
                    marginX = 62f,
                    marginY = 62f,
                    flipTime = newConfigFlipTime.value,
                    stations = stationsList
                )

                // Save configuration
                val updatedConfigs = configurations.value + newConfig
                appViewModel.saveBlisterConfigurations(updatedConfigs)

                // Clear form and refresh list
                clearNewConfigForm()
                refreshConfigurations()
                _successMessage.value = "Konfiguration '${newConfig.name}' erfolgreich hinzugefügt"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Hinzufügen der Konfiguration: ${e.message}"
            }
        }
    }

    fun updateConfiguration() {
        viewModelScope.launch {
            try {
                val selectedConfig = selectedConfiguration.value ?: return@launch

                // Validate input
                if (editConfigName.value.isBlank()) {
                    _errorMessage.value = "Konfigurationsname darf nicht leer sein"
                    return@launch
                }

                // Check if name changed and new name already exists
                if (editConfigName.value != selectedConfig.name &&
                    configurations.value.any { it.name == editConfigName.value }) {
                    _errorMessage.value = "Eine Konfiguration mit diesem Namen existiert bereits"
                    return@launch
                }

                // Parse stations
                val stationsList = if (editConfigStations.value.isBlank()) {
                    emptyList()
                } else {
                    editConfigStations.value.split(",").map { it.trim() }.filter { it.isNotBlank() }
                }

                // Create updated row heights if rows changed
                val updatedRowHeights = if (editConfigRows.value != selectedConfig.rowHeight.size) {
                    List(editConfigRows.value) { 190f }
                } else {
                    selectedConfig.rowHeight
                }

                // Create updated configuration
                val updatedConfig = selectedConfig.copy(
                    name = editConfigName.value,
                    rowHeight = updatedRowHeights,
                    flipTime = editConfigFlipTime.value,
                    stations = stationsList
                )

                // Save configuration
                val updatedConfigs = configurations.value.map {
                    if (it.name == selectedConfig.name) updatedConfig else it
                }
                appViewModel.saveBlisterConfigurations(updatedConfigs)

                // Clear form and refresh list
                cancelEditing()
                refreshConfigurations()
                _successMessage.value = "Konfiguration '${updatedConfig.name}' erfolgreich aktualisiert"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Aktualisieren der Konfiguration: ${e.message}"
            }
        }
    }

    fun removeConfiguration(config: BlisterConfiguration) {
        viewModelScope.launch {
            try {
                val updatedConfigs = configurations.value.filter { it.name != config.name }
                appViewModel.saveBlisterConfigurations(updatedConfigs)
                refreshConfigurations()
                _successMessage.value = "Konfiguration '${config.name}' erfolgreich gelöscht"
            } catch (e: Exception) {
                _errorMessage.value = "Fehler beim Löschen der Konfiguration: ${e.message}"
            }
        }
    }

    fun openCalibration(config: BlisterConfiguration) {
        val calibrationViewModel = CalibrationViewModel(appViewModel)
        calibrationViewModel.selectBlisterConfig(config)
        calibrationViewModel.present(appViewModel)
    }

    override fun handleBarcode(barcode: String) {
        // Not implemented for this view
    }

    override fun handleActionKey(key: Long) {
        // Not implemented for this view
    }

    companion object {
        fun present(appViewModel: AppViewModel) {
            appViewModel.navigation.push(BlisterConfigurationManagementViewModel(appViewModel))
        }
    }
}
